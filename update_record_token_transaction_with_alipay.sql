-- 更新充值存储过程以支持支付宝交易号
CREATE OR REPLACE FUNCTION public.record_token_transaction_with_alipay(
    p_user_id uuid,           -- 用户ID
    p_amount integer,         -- 充值金额（正数）
    p_description text,       -- 交易描述
    p_transaction_type smallint,  -- 交易类型（2表示充值）
    p_order_id varchar(64),   -- 订单号
    p_alipay_trade_no varchar(64) DEFAULT NULL  -- 支付宝交易号（可选）
)
RETURNS jsonb AS $$
DECLARE
    v_current_balance integer;
    v_new_balance integer;
    v_transaction_id bigint;
    v_created_at BIGINT;
    v_result jsonb;
BEGIN
    -- 参数验证
    IF p_amount <= 0 THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', '充值金额必须大于0',
            'code', 'INVALID_AMOUNT'
        );
    END IF;

    IF p_transaction_type != 2 THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', '无效的交易类型',
            'code', 'INVALID_TRANSACTION_TYPE'
        );
    END IF;

    -- 检查订单重复
    IF EXISTS (SELECT 1 FROM public.token_transactions WHERE order_id = p_order_id) THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', '订单已存在',
            'code', 'DUPLICATE_ORDER'
        );
    END IF;

    -- 检查支付宝交易号重复（如果提供了的话）
    IF p_alipay_trade_no IS NOT NULL AND EXISTS (
        SELECT 1 FROM public.token_transactions 
        WHERE alipay_trade_no = p_alipay_trade_no
    ) THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', '支付宝交易号已存在',
            'code', 'DUPLICATE_ALIPAY_TRADE_NO'
        );
    END IF;

    -- 获取并锁定当前余额
    SELECT balance INTO v_current_balance
    FROM public.user_tokens
    WHERE user_id = p_user_id
    FOR UPDATE;

    -- 处理新用户情况
    IF v_current_balance IS NULL THEN
        INSERT INTO public.user_tokens (user_id, balance)
        VALUES (p_user_id, 0)
        RETURNING balance INTO v_current_balance;
    END IF;

    -- 更新余额
    v_new_balance := v_current_balance + p_amount;
    UPDATE public.user_tokens
    SET balance = v_new_balance
    WHERE user_id = p_user_id;

    -- 记录交易（包含支付宝交易号）
    INSERT INTO public.token_transactions
        (user_id, amount, balance_after, description, transaction_type, order_id, alipay_trade_no)
    VALUES
        (p_user_id, p_amount, v_new_balance, p_description, p_transaction_type, p_order_id, p_alipay_trade_no)
    RETURNING id, created_at INTO v_transaction_id, v_created_at;

    -- 返回结果
    RETURN jsonb_build_object(
        'success', true,
        'data', jsonb_build_object(
            'transaction_id', v_transaction_id,
            'order_id', p_order_id,
            'alipay_trade_no', p_alipay_trade_no,
            'balance_after', v_new_balance,
            'amount', p_amount,
            'description', p_description,
            'created_at', v_created_at
        )
    );

EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'code', SQLSTATE
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 授予存储过程执行权限
GRANT EXECUTE ON FUNCTION public.record_token_transaction_with_alipay TO service_role;
