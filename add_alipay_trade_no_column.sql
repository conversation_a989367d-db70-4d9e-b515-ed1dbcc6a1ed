-- 为 token_transactions 表添加支付宝交易号字段
-- 这个字段用于存储支付宝返回的 trade_no，便于对账和问题排查

-- 添加支付宝交易号字段
ALTER TABLE public.token_transactions 
ADD COLUMN alipay_trade_no varchar(64);

-- 添加注释
COMMENT ON COLUMN public.token_transactions.alipay_trade_no IS '支付宝交易号，用于对账和问题排查';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_token_transactions_alipay_trade_no
    ON public.token_transactions(alipay_trade_no)
    WHERE alipay_trade_no IS NOT NULL;

-- 添加唯一约束，防止同一个支付宝交易号被重复处理
-- 注意：这里使用部分唯一索引，只对非空值生效
CREATE UNIQUE INDEX IF NOT EXISTS idx_token_transactions_alipay_trade_no_unique
    ON public.token_transactions(alipay_trade_no)
    WHERE alipay_trade_no IS NOT NULL;
