#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
支付宝回调接口测试脚本
用于测试支付宝异步通知处理逻辑
"""

import asyncio
import httpx
import json
from datetime import datetime


async def test_alipay_notify():
    """测试支付宝回调接口"""
    
    # 测试数据 - 模拟支付宝回调参数
    test_data = {
        # 基本参数
        "trade_status": "TRADE_SUCCESS",  # 交易状态
        "out_trade_no": "RC20241225123456_550e8400-e29b-41d4-a716-446655440000_1000",  # 商户订单号
        "total_amount": "10.00",  # 交易金额（元）
        "trade_no": "2024122522001234567890123456",  # 支付宝交易号
        "buyer_id": "2088123456789012",  # 买家支付宝用户ID
        
        # 其他可能的参数
        "app_id": "2021005163655913",
        "charset": "utf-8",
        "method": "alipay.trade.page.pay.return",
        "sign_type": "RSA2",
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "version": "1.0",
        
        # 商品信息
        "subject": "算力充值",
        "body": "用户充值10元获得10算力",
        
        # 时间信息
        "gmt_create": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "gmt_payment": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        
        # 签名（实际使用时需要真实签名）
        "sign": "test_signature_here"
    }
    
    # API 端点
    url = "http://localhost:8000/api/v1/alipay/notify"
    
    print("=" * 60)
    print("支付宝回调接口测试")
    print("=" * 60)
    print(f"测试URL: {url}")
    print(f"测试数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
    print("=" * 60)
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            # 发送 POST 请求，模拟支付宝回调
            response = await client.post(
                url,
                data=test_data,  # 使用 form data 格式
                headers={
                    "Content-Type": "application/x-www-form-urlencoded",
                    "User-Agent": "Alipay-Notify/1.0"
                }
            )
            
            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                if response.text.strip() == "success":
                    print("✅ 测试成功：返回了正确的 success 响应")
                else:
                    print("⚠️  警告：响应内容不是预期的 'success'")
            else:
                print("❌ 测试失败：HTTP状态码不是200")
                try:
                    error_data = response.json()
                    print(f"错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
                except:
                    print(f"无法解析错误响应: {response.text}")
                    
    except Exception as e:
        print(f"❌ 请求失败: {e}")


async def test_invalid_order_format():
    """测试无效订单号格式"""
    
    test_data = {
        "trade_status": "TRADE_SUCCESS",
        "out_trade_no": "INVALID_ORDER_FORMAT_123",  # 无效的订单号格式
        "total_amount": "10.00",
        "trade_no": "2024122522001234567890123456",
        "buyer_id": "2088123456789012"
    }
    
    url = "http://localhost:8000/api/v1/alipay/notify"
    
    print("\n" + "=" * 60)
    print("测试无效订单号格式")
    print("=" * 60)
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(url, data=test_data)
            
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 400:
                print("✅ 测试成功：正确处理了无效订单号格式")
            else:
                print("⚠️  可能的问题：未正确处理无效订单号")
                
    except Exception as e:
        print(f"❌ 请求失败: {e}")


async def test_missing_params():
    """测试缺少必要参数"""
    
    test_data = {
        "trade_status": "TRADE_SUCCESS",
        # 缺少 out_trade_no
        "total_amount": "10.00",
        "trade_no": "2024122522001234567890123456"
    }
    
    url = "http://localhost:8000/api/v1/alipay/notify"
    
    print("\n" + "=" * 60)
    print("测试缺少必要参数")
    print("=" * 60)
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(url, data=test_data)
            
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 400:
                print("✅ 测试成功：正确处理了缺少参数的情况")
            else:
                print("⚠️  可能的问题：未正确处理缺少参数")
                
    except Exception as e:
        print(f"❌ 请求失败: {e}")


if __name__ == "__main__":
    print("开始测试支付宝回调接口...")
    print("注意：请确保服务器正在运行在 http://localhost:8000")
    print("注意：请确保数据库中存在测试用户ID: 550e8400-e29b-41d4-a716-446655440000")
    
    asyncio.run(test_alipay_notify())
    asyncio.run(test_invalid_order_format())
    asyncio.run(test_missing_params())
    
    print("\n测试完成！")
