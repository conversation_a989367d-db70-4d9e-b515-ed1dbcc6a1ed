from fastapi import APIRouter, Request
import re
from typing import Optional, <PERSON><PERSON>

from app.models.schemas import ErrorResponse, ResponseJson
from app.db.supabase_singleton import supabase
from utils.logger import get_logger

router = APIRouter()

logs = get_logger()


def parse_order_info(out_trade_no: str) -> Optional[Tuple[str, int]]:
    """
    从支付宝订单号中解析用户ID和充值金额

    订单号格式假设为: RC{timestamp}_{user_id}_{amount}
    或者其他自定义格式，需要根据实际业务调整

    Args:
        out_trade_no: 支付宝订单号

    Returns:
        Tuple[user_id, amount] 或 None（如果解析失败）
    """
    try:
        # 假设订单号格式为: RC{timestamp}_{user_id}_{amount}
        # 例如: RC20241225123456_550e8400-e29b-41d4-a716-446655440000_1000
        pattern = r'^RC\d+_([a-f0-9-]{36})_(\d+)$'
        match = re.match(pattern, out_trade_no)

        if match:
            user_id = match.group(1)
            amount = int(match.group(2))
            return user_id, amount

        # 如果上述格式不匹配，尝试其他可能的格式
        # 可以根据实际业务需求添加更多解析逻辑
        logs.warning(f"无法解析订单号格式: {out_trade_no}")
        return None

    except Exception as e:
        logs.error(f"解析订单号失败: {out_trade_no}, 错误: {e}")
        return None


def get_token_amount_from_price(total_amount: str) -> int:
    """
    根据支付金额计算应该充值的代币数量

    Args:
        total_amount: 支付金额（元，字符串格式）

    Returns:
        int: 应该充值的代币数量
    """
    try:
        # 将金额转换为分
        amount_cents = int(float(total_amount) * 100)

        # 查询汇率表获取对应的代币数量
        response = supabase.table("token_exchange_rates")\
            .select("token_amount")\
            .eq("price", amount_cents)\
            .eq("status", 1)\
            .execute()

        if response.data and len(response.data) > 0:
            return response.data[0]["token_amount"]

        # 如果没有找到对应的汇率，使用默认比例（1元=1代币）
        logs.warning(f"未找到金额 {amount_cents} 分对应的汇率，使用默认比例")
        return int(float(total_amount))

    except Exception as e:
        logs.error(f"计算代币数量失败: {total_amount}, 错误: {e}")
        # 返回默认值
        return int(float(total_amount)) if total_amount else 0


@router.post("/notify")
async def alipay_notify(request: Request):
    """
    接收支付宝异步回调通知
    """
    # 1. 获取并解析表单数据
    try:
        form = await request.form()
        data = dict(form)
        logs.info(f"收到支付宝回调原始数据: {data}")
    except Exception as e:
        logs.error(f"解析支付宝回调表单失败: {e}")
        # 如果解析失败，不应返回 success，避免支付宝重试风暴
        return ResponseJson(
            code=400,
            message="error",
            data=ErrorResponse(
                status="ALIPAY_NOTIFY_PARSE_FAILED",
                detail="解析支付宝回调表单失败"
            )
        )

    # 2. 读取关键信息
    trade_status = data.get("trade_status")
    out_trade_no = data.get("out_trade_no")
    total_amount = data.get("total_amount")
    trade_no = data.get("trade_no")
    buyer_id = data.get("buyer_id")  # 支付宝买家ID

    logs.info(f"支付宝回调: 单号={out_trade_no} 状态={trade_status} 金额={total_amount} 支付宝单号={trade_no}")

    # 3. 验证必要参数
    if not all([trade_status, out_trade_no, total_amount, trade_no]):
        logs.error("支付宝回调缺少必要参数")
        return ResponseJson(
            code=400,
            message="error",
            data=ErrorResponse(
                status="MISSING_REQUIRED_PARAMS",
                detail="缺少必要的回调参数"
            )
        )

    # 4. 按需处理业务逻辑
    if trade_status in ("TRADE_SUCCESS", "TRADE_FINISHED"):
        try:
            # 4.1 解析订单信息获取用户ID
            order_info = parse_order_info(out_trade_no)
            if not order_info:
                # 如果无法从订单号解析用户信息，尝试从数据库查找
                logs.warning(f"无法从订单号解析用户信息，尝试从数据库查找: {out_trade_no}")

                # 查询是否已存在该订单
                existing_order = supabase.table("token_transactions")\
                    .select("user_id, amount")\
                    .eq("order_id", out_trade_no)\
                    .execute()

                if existing_order.data:
                    logs.info(f"订单已存在，跳过处理: {out_trade_no}")
                    return "success"
                else:
                    logs.error(f"无法处理订单，找不到用户信息: {out_trade_no}")
                    return ResponseJson(
                        code=400,
                        message="error",
                        data=ErrorResponse(
                            status="INVALID_ORDER_FORMAT",
                            detail="无法解析订单信息"
                        )
                    )

            user_id, expected_amount = order_info

            # 4.2 验证金额是否匹配（可选，增强安全性）
            actual_amount_cents = int(float(total_amount) * 100)
            if expected_amount != actual_amount_cents:
                logs.warning(f"订单金额不匹配: 期望={expected_amount}, 实际={actual_amount_cents}")

            # 4.3 计算应充值的代币数量
            token_amount = get_token_amount_from_price(total_amount)

            # 4.4 检查订单是否已处理（防重复）
            existing_transaction = supabase.table("token_transactions")\
                .select("id")\
                .eq("order_id", out_trade_no)\
                .execute()

            if existing_transaction.data:
                logs.info(f"订单已处理，跳过: {out_trade_no}")
                return "success"

            # 4.5 调用存储过程记录充值交易
            logs.info(f"开始处理充值: 用户={user_id}, 金额={token_amount}, 订单={out_trade_no}, 支付宝单号={trade_no}")

            # 优先使用支持支付宝交易号的新存储过程
            try:
                result = supabase.rpc(
                    'record_token_transaction_with_alipay',
                    {
                        'p_user_id': user_id,
                        'p_amount': token_amount,
                        'p_description': f'支付宝充值 - 订单号:{out_trade_no}',
                        'p_transaction_type': 2,  # 2表示充值
                        'p_order_id': out_trade_no,
                        'p_alipay_trade_no': trade_no
                    }
                ).execute()
            except Exception as proc_error:
                # 如果新存储过程不存在，回退到旧的存储过程
                logs.warning(f"新存储过程调用失败，回退到旧版本: {proc_error}")
                result = supabase.rpc(
                    'record_token_transaction',
                    {
                        'p_user_id': user_id,
                        'p_amount': token_amount,
                        'p_description': f'支付宝充值 - 订单号:{out_trade_no} - 支付宝单号:{trade_no}',
                        'p_transaction_type': 2,  # 2表示充值
                        'p_order_id': out_trade_no
                    }
                ).execute()

            if result.data and result.data.get('success'):
                transaction_data = result.data.get('data', {})
                logs.info(f"充值成功: 用户={user_id}, 代币={token_amount}, 交易ID={transaction_data.get('transaction_id')}")

                # TODO: 可以在这里添加其他业务逻辑，如发送通知等

            else:
                error_msg = result.data.get('error', '未知错误') if result.data else '存储过程执行失败'
                logs.error(f"充值失败: {error_msg}")
                return ResponseJson(
                    code=500,
                    message="error",
                    data=ErrorResponse(
                        status="RECHARGE_FAILED",
                        detail=f"充值处理失败: {error_msg}"
                    )
                )

        except Exception as e:
            logs.error(f"处理支付宝回调业务逻辑失败: {e}", exc_info=True)
            return ResponseJson(
                code=500,
                message="error",
                data=ErrorResponse(
                    status="BUSINESS_LOGIC_ERROR",
                    detail="处理业务逻辑时发生错误"
                )
            )
    else:
        logs.info(f"支付状态不需要处理: {trade_status}")

    # 4. 返回字符串 success，必须是纯文本且无额外空格/换行
    return "success"
